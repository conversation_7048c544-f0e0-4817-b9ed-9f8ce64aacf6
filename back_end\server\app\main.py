"""
Phoenix Pipeline Server - Main Application
FastAPI application with WebSocket support
"""

import json
import logging
from datetime import datetime
from typing import Dict
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.router import api_router
from app.api.dependencies import init_services
from app.services.websocket_service import WebSocketManager
from app.services.pipeline_service import PipelineService

# Setup logging
logger = setup_logging()

# Global service instances
websocket_manager: WebSocketManager = None
pipeline_service: PipelineService = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global websocket_manager, pipeline_service
    
    # Startup
    logger.info("🚀 Phoenix Pipeline Server starting up...")
    
    # Initialize services
    websocket_manager = WebSocketManager()
    pipeline_service = PipelineService(websocket_manager)
    
    # Initialize dependencies
    init_services(websocket_manager, pipeline_service)
    
    # Initialize pipeline service
    await pipeline_service.initialize()
    
    logger.info("✅ Server initialization complete")
    
    yield
    
    # Shutdown
    logger.info("🛑 Phoenix Pipeline Server shutting down...")
    await websocket_manager.disconnect_all()
    await pipeline_service.cleanup()
    logger.info("✅ Server shutdown complete")

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Root endpoint
@app.get("/")
async def root():
    """API root - basic server info"""
    return {
        "name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "description": settings.APP_DESCRIPTION,
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "api_base": "/api/v1",
            "websocket": "/ws",
            "health": "/api/v1/health",
            "docs": "/docs",
            "redoc": "/redoc"
        },
        "api_endpoints": {
            "pipeline_status": "GET /api/v1/pipeline/status",
            "pipeline_start": "POST /api/v1/pipeline/start",
            "pipeline_stop": "POST /api/v1/pipeline/stop",
            "pipeline_pause": "POST /api/v1/pipeline/pause",
            "pipeline_resume": "POST /api/v1/pipeline/resume",
            "websocket_stats": "GET /api/v1/websocket/stats"
        }
    }

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for real-time communication"""
    client_id = None
    try:
        # Accept connection
        await websocket.accept()
        client_id = await websocket_manager.connect(websocket)
        
        logger.info(f"🔌 Client {client_id} connected")
        
        # Send initial connection status
        await websocket_manager.send_to_client(client_id, {
            "type": "connection_status",
            "data": {
                "status": "connected",
                "client_id": client_id,
                "timestamp": datetime.now().isoformat()
            }
        })
        
        # Send current pipeline status
        current_status = pipeline_service.get_current_status()
        await websocket_manager.send_to_client(client_id, {
            "type": "pipeline_status",
            "data": current_status
        })
        
        # Listen for messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                logger.info(f"📨 Received from {client_id}: {message}")
                
                # Handle different message types
                await handle_client_message(client_id, message)
                
            except json.JSONDecodeError:
                logger.error(f"❌ Invalid JSON from client {client_id}")
                await websocket_manager.send_to_client(client_id, {
                    "type": "error",
                    "data": {"message": "Invalid JSON format"}
                })
            except Exception as e:
                logger.error(f"❌ Error processing message from {client_id}: {e}")
                break
                
    except WebSocketDisconnect:
        logger.info(f"🔌 Client {client_id} disconnected")
    except Exception as e:
        logger.error(f"❌ WebSocket error for client {client_id}: {e}")
    finally:
        if client_id:
            await websocket_manager.disconnect(client_id)

async def handle_client_message(client_id: str, message: Dict):
    """Handle incoming messages from WebSocket clients"""
    message_type = message.get("type")
    data = message.get("data", {})
    
    try:
        if message_type == "ping":
            # Respond to ping with pong
            await websocket_manager.send_to_client(client_id, {
                "type": "pong",
                "data": {"timestamp": datetime.now().isoformat()}
            })
            
        elif message_type == "pipeline_start":
            # Start pipeline execution
            document_id = data.get("document_id")
            if not document_id:
                raise ValueError("document_id is required")
                
            await pipeline_service.start_pipeline(document_id)
            logger.info(f"🚀 Pipeline started by client {client_id}")
            
        elif message_type == "pipeline_stop":
            # Stop pipeline execution
            await pipeline_service.stop_pipeline()
            logger.info(f"🛑 Pipeline stopped by client {client_id}")
            
        elif message_type == "get_status":
            # Send current pipeline status
            current_status = pipeline_service.get_current_status()
            await websocket_manager.send_to_client(client_id, {
                "type": "pipeline_status",
                "data": current_status
            })
            
        else:
            logger.warning(f"⚠️ Unknown message type from {client_id}: {message_type}")
            await websocket_manager.send_to_client(client_id, {
                "type": "error",
                "data": {"message": f"Unknown message type: {message_type}"}
            })
            
    except Exception as e:
        logger.error(f"❌ Error handling message from {client_id}: {e}")
        await websocket_manager.send_to_client(client_id, {
            "type": "error",
            "data": {"message": str(e)}
        })

# Include API router
app.include_router(api_router)
