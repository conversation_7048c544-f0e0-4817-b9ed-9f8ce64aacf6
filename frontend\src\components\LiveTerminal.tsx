/**
 * LiveTerminal Component - Resizable Terminal with xterm.js
 * Real terminal using xterm.js library
 */
import React, { useState, useRef, useCallback, useEffect } from "react";
import { Search, Copy, Download, Maximize2, Wifi, Move } from "lucide-react";
import { Terminal } from "xterm";
import { FitAddon } from "xterm-addon-fit";
import { SearchAddon } from "xterm-addon-search";
import { WebLinksAddon } from "xterm-addon-web-links";
import "xterm/css/xterm.css";

interface LiveTerminalProps {
    height?: number;
    onResize?: (height: number) => void;
    className?: string;
}

export const LiveTerminal: React.FC<LiveTerminalProps> = ({
    height: initialHeight = 300,
    onResize,
    className = "",
}) => {
    const [height, setHeight] = useState(initialHeight);
    const [isResizing, setIsResizing] = useState(false);
    const [isConnected, setIsConnected] = useState(false);
    const startY = useRef(0);
    const startHeight = useRef(0);
    const terminalRef = useRef<HTMLDivElement>(null);
    const xtermRef = useRef<Terminal | null>(null);
    const fitAddonRef = useRef<FitAddon | null>(null);
    const searchAddonRef = useRef<SearchAddon | null>(null);

    // Initialize xterm.js terminal
    useEffect(() => {
        if (!terminalRef.current) return;

        // Create terminal instance
        const terminal = new Terminal({
            theme: {
                background: "#000000",
                foreground: "#00ff00",
                cursor: "#00ff00",
                cursorAccent: "#000000",
                selectionBackground: "rgba(255, 255, 255, 0.3)",
            },
            fontSize: 14,
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            cursorBlink: true,
            allowTransparency: true,
        });

        // Create addons
        const fitAddon = new FitAddon();
        const searchAddon = new SearchAddon();
        const webLinksAddon = new WebLinksAddon();

        // Load addons
        terminal.loadAddon(fitAddon);
        terminal.loadAddon(searchAddon);
        terminal.loadAddon(webLinksAddon);

        // Open terminal
        terminal.open(terminalRef.current);

        // Store references
        xtermRef.current = terminal;
        fitAddonRef.current = fitAddon;
        searchAddonRef.current = searchAddon;

        // Initial content
        terminal.writeln("Welcome to Phoenix Pipeline Terminal");
        terminal.writeln("Connected to pipeline execution engine...");
        terminal.write("$ ");

        // Simulate connection and demo content
        setTimeout(() => {
            setIsConnected(true);
            terminal.writeln("Connection established ✓");
            terminal.writeln("$ pipeline status");
            terminal.writeln("📄 Document Processor: ✅ COMPLETED");
            terminal.writeln("🔍 Business Flow Detector: ⏳ RUNNING...");
            terminal.writeln("📊 Extract and Process to JSON: ❌ FAILED");
            terminal.writeln("🏗️  Generate HTML Structure UIDL: ⏸️ PENDING");
            terminal.writeln("🧪 Generate Test Case: ⚠️ WARNING");
            terminal.writeln("✅ Test Case Evaluator: ⏸️ PAUSED");
            terminal.writeln("");
            terminal.write("$ ");
        }, 1000);

        // Add more demo content
        setTimeout(() => {
            terminal.writeln("pipeline logs --step=business_flow_detector");
            terminal.writeln("[INFO] Processing business flow patterns...");
            terminal.writeln("[INFO] Analyzing document structure...");
            terminal.writeln("[WARN] Complex nested structure detected");
            terminal.writeln("[INFO] Flow detection: 85% complete");
            terminal.write("$ ");
        }, 3000);

        // Fit terminal to container
        fitAddon.fit();

        // Cleanup
        return () => {
            terminal.dispose();
        };
    }, []);

    // Handle resize
    const handleMouseDown = useCallback(
        (e: React.MouseEvent) => {
            setIsResizing(true);
            startY.current = e.clientY;
            startHeight.current = height;

            const handleMouseMove = (e: MouseEvent) => {
                const deltaY = startY.current - e.clientY;
                const newHeight = Math.max(
                    150,
                    Math.min(800, startHeight.current + deltaY)
                );

                setHeight(newHeight);
                onResize?.(newHeight);

                // Fit terminal after resize
                setTimeout(() => {
                    fitAddonRef.current?.fit();
                }, 0);
            };

            const handleMouseUp = () => {
                setIsResizing(false);
                document.removeEventListener("mousemove", handleMouseMove);
                document.removeEventListener("mouseup", handleMouseUp);
            };

            document.addEventListener("mousemove", handleMouseMove);
            document.addEventListener("mouseup", handleMouseUp);
            e.preventDefault();
        },
        [height, onResize]
    );

    // Fit terminal when height changes
    useEffect(() => {
        if (fitAddonRef.current) {
            setTimeout(() => {
                fitAddonRef.current?.fit();
            }, 100);
        }
    }, [height]);

    // Handle button actions
    const handleSearch = useCallback(() => {
        // You can implement search functionality here
        console.log("Search in terminal");
    }, []);

    const handleCopy = useCallback(() => {
        if (xtermRef.current) {
            const selection = xtermRef.current.getSelection();
            if (selection) {
                navigator.clipboard.writeText(selection);
                console.log("Copied to clipboard:", selection);
            }
        }
    }, []);

    const handleDownload = useCallback(() => {
        if (xtermRef.current) {
            const content =
                xtermRef.current.buffer.active
                    .getLine(0)
                    ?.translateToString() || "";
            const blob = new Blob([content], { type: "text/plain" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "terminal-output.txt";
            a.click();
            URL.revokeObjectURL(url);
        }
    }, []);

    const handleMaximize = useCallback(() => {
        const newHeight = height === 600 ? 300 : 600;
        setHeight(newHeight);
        onResize?.(newHeight);
    }, [height, onResize]);

    return (
        <div
            className={`bg-gray-900 border border-gray-700 rounded-lg ${className} ${
                isResizing ? "select-none" : ""
            }`}
        >
            {/* Resize Handle */}
            <div
                className="h-3 bg-gray-700 hover:bg-blue-500 cursor-row-resize transition-colors relative group flex items-center justify-center border-b border-gray-600"
                onMouseDown={handleMouseDown}
                style={{ userSelect: "none" }}
            >
                <Move className="w-4 h-4 text-gray-400 group-hover:text-blue-300 rotate-90" />
                <div className="absolute inset-0 -top-1 -bottom-1" />
            </div>

            {/* Terminal Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-700 bg-gray-800">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                        <Wifi className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-300">Terminal</span>
                    </div>
                    <div className="text-xs text-gray-500">
                        {isConnected ? "Connected" : "Connecting..."} • Height:{" "}
                        {height}px
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <button
                        onClick={handleSearch}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Search in terminal"
                    >
                        <Search className="w-4 h-4" />
                    </button>
                    <button
                        onClick={handleCopy}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Copy selection"
                    >
                        <Copy className="w-4 h-4" />
                    </button>
                    <button
                        onClick={handleDownload}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Download terminal output"
                    >
                        <Download className="w-4 h-4" />
                    </button>
                    <button
                        onClick={handleMaximize}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Toggle maximize"
                    >
                        <Maximize2 className="w-4 h-4" />
                    </button>
                </div>
            </div>

            {/* Terminal Content - xterm.js */}
            <div
                ref={terminalRef}
                className="bg-black"
                style={{ height: `${height}px` }}
            />
        </div>
    );
};
