@tailwind base;
@tailwind components;
@tailwind utilities;

/* xterm.js terminal styles */
.xterm {
    font-feature-settings: "liga" 0;
    position: relative;
    user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
}

.xterm.focus,
.xterm:focus {
    outline: none;
}

.xterm .xterm-helpers {
    position: absolute;
    top: 0;
    z-index: 5;
}

.xterm .xterm-helper-textarea {
    position: absolute;
    opacity: 0;
    left: -9999em;
    top: 0;
    width: 0;
    height: 0;
    z-index: -5;
    white-space: nowrap;
    overflow: hidden;
    resize: none;
}

.xterm .composition-view {
    background: #000;
    color: #fff;
    display: none;
    position: absolute;
    white-space: nowrap;
    z-index: 1;
}

.xterm .composition-view.active {
    display: block;
}

.xterm .xterm-viewport {
    background-color: #000;
    overflow-y: scroll;
    cursor: default;
    position: absolute;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
}

.xterm .xterm-screen {
    position: relative;
}

.xterm .xterm-screen canvas {
    position: absolute;
    left: 0;
    top: 0;
}

.xterm .xterm-scroll-area {
    visibility: hidden;
}

.xterm-char-measure-element {
    display: inline-block;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: -9999em;
    line-height: normal;
}

.xterm.enable-mouse-events {
    cursor: default;
}

.xterm.xterm-cursor-pointer {
    cursor: pointer;
}

.xterm.column-select.focus {
    cursor: crosshair;
}

.xterm .xterm-accessibility,
.xterm .xterm-message {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 10;
    color: transparent;
}

.xterm .live-region {
    position: absolute;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Professional Pipeline Node Animations */
@keyframes gradient-x {
    0%,
    100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.animate-gradient-x {
    background-size: 200% 200%;
    animation: gradient-x 3s ease infinite;
}

@layer base {
    * {
        @apply border-dark-700;
    }

    body {
        @apply bg-dark-950 text-white font-sans;
        font-feature-settings: "rlig" 1, "calt" 1;
    }

    /* Custom scrollbar for dark theme */
    ::-webkit-scrollbar {
        @apply w-2 h-2;
    }

    ::-webkit-scrollbar-track {
        @apply bg-dark-900;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-dark-600 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-dark-500;
    }
}

@layer components {
    .glass-panel {
        @apply bg-dark-900/80 backdrop-blur-sm border border-dark-700/50;
    }

    .card {
        @apply bg-dark-900 border border-dark-700 rounded-lg;
    }

    .btn-primary {
        @apply bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md transition-colors;
    }

    .btn-secondary {
        @apply bg-dark-700 hover:bg-dark-600 text-white px-4 py-2 rounded-md transition-colors;
    }

    .input {
        @apply bg-dark-800 border border-dark-600 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
    }

    .status-pending {
        @apply bg-dark-600 text-dark-200;
    }

    .status-running {
        @apply bg-primary-600 text-white animate-pulse-glow;
    }

    .status-success {
        @apply bg-success-600 text-white;
    }

    .status-failed {
        @apply bg-error-600 text-white;
    }
}

/* React Flow dark theme overrides */
.react-flow__node {
    @apply bg-dark-800 border-dark-600 text-white;
}

.react-flow__edge-path {
    @apply stroke-dark-400;
}

.react-flow__edge.animated .react-flow__edge-path {
    @apply stroke-primary-500;
    stroke-dasharray: 5;
    animation: dashdraw 0.5s linear infinite;
}

.react-flow__controls {
    @apply bg-dark-800 border-dark-600;
}

.react-flow__controls button {
    @apply bg-dark-700 border-dark-600 text-white hover:bg-dark-600;
}

.react-flow__minimap {
    @apply bg-dark-800 border-dark-600;
}

@keyframes dashdraw {
    to {
        stroke-dashoffset: -10;
    }
}

/* AG Grid dark theme - applied inline */
