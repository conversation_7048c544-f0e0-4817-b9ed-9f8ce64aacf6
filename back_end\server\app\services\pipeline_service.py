"""
Pipeline Service
Manages pipeline execution and real-time status updates
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, Optional, Any
import random

from app.models.schemas import (
    PipelineStatus, PipelineStep, PipelineStepStatus,
    LogMessage, LogLevel, create_default_pipeline_status
)

logger = logging.getLogger(__name__)

class PipelineService:
    """Service for managing pipeline execution"""
    
    def __init__(self, websocket_manager):
        self.websocket_manager = websocket_manager
        self.current_pipeline: Optional[PipelineStatus] = None
        self.execution_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        logger.info("🔧 PipelineService initialized")

    async def initialize(self):
        """Initialize the pipeline service"""
        # Create a default pipeline status
        pipeline_id = str(uuid.uuid4())
        self.current_pipeline = create_default_pipeline_status(pipeline_id)
        
        # Set some demo statuses to match frontend
        self.current_pipeline.update_step_status("document_processor", PipelineStepStatus.COMPLETED, progress=100.0)
        self.current_pipeline.update_step_status("business_flow_detector", PipelineStepStatus.RUNNING, progress=65.0)
        self.current_pipeline.update_step_status("extract_and_process_to_json", PipelineStepStatus.FAILED, progress=0.0)
        self.current_pipeline.update_step_status("gen_html_structure_uidl", PipelineStepStatus.PENDING, progress=0.0)
        self.current_pipeline.update_step_status("gen_test_case", PipelineStepStatus.WARNING, progress=45.0)
        self.current_pipeline.update_step_status("test_case_evaluator", PipelineStepStatus.PAUSED, progress=0.0)
        
        self.current_pipeline.overall_progress = self.current_pipeline.calculate_overall_progress()
        self.current_pipeline.current_step = "business_flow_detector"
        
        logger.info("✅ Pipeline service initialized with demo status")

    async def cleanup(self):
        """Cleanup pipeline service"""
        if self.execution_task and not self.execution_task.done():
            self.execution_task.cancel()
            try:
                await self.execution_task
            except asyncio.CancelledError:
                pass
        logger.info("🧹 Pipeline service cleaned up")

    def get_current_status(self) -> Dict[str, Any]:
        """Get current pipeline status as dict"""
        if not self.current_pipeline:
            return {}
        return self.current_pipeline.dict()

    async def start_pipeline(self, document_id: str):
        """Start pipeline execution"""
        if self.is_running:
            raise ValueError("Pipeline is already running")
        
        # Create new pipeline instance
        pipeline_id = str(uuid.uuid4())
        self.current_pipeline = create_default_pipeline_status(pipeline_id, document_id)
        self.current_pipeline.start_time = datetime.now()
        self.current_pipeline.status = PipelineStepStatus.RUNNING
        
        self.is_running = True
        
        # Start execution task
        self.execution_task = asyncio.create_task(self._execute_pipeline())
        
        # Broadcast pipeline started
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution started", LogLevel.INFO)
        
        logger.info(f"🚀 Pipeline {pipeline_id} started for document {document_id}")

    async def stop_pipeline(self):
        """Stop pipeline execution"""
        if not self.is_running:
            raise ValueError("No pipeline is currently running")
        
        self.is_running = False
        
        if self.execution_task and not self.execution_task.done():
            self.execution_task.cancel()
        
        if self.current_pipeline:
            self.current_pipeline.status = PipelineStepStatus.CANCELLED
            self.current_pipeline.end_time = datetime.now()
            
            # Cancel current step
            if self.current_pipeline.current_step:
                self.current_pipeline.update_step_status(
                    self.current_pipeline.current_step, 
                    PipelineStepStatus.CANCELLED
                )
        
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution stopped", LogLevel.WARNING)
        
        logger.info("🛑 Pipeline execution stopped")

    async def _execute_pipeline(self):
        """Execute the pipeline steps"""
        try:
            if not self.current_pipeline:
                return
            
            for step in self.current_pipeline.steps:
                if not self.is_running:
                    break
                
                # Update current step
                self.current_pipeline.current_step = step.id
                
                # Start step execution
                await self._execute_step(step)
                
                # Small delay between steps
                await asyncio.sleep(1)
            
            # Pipeline completed
            if self.is_running:
                self.current_pipeline.status = PipelineStepStatus.COMPLETED
                self.current_pipeline.end_time = datetime.now()
                self.current_pipeline.current_step = None
                
                if self.current_pipeline.start_time:
                    duration = (self.current_pipeline.end_time - self.current_pipeline.start_time).total_seconds()
                    self.current_pipeline.total_duration = duration
                
                await self._broadcast_status_update()
                await self._broadcast_log("Pipeline execution completed successfully", LogLevel.INFO)
                
                logger.info("✅ Pipeline execution completed")
            
        except asyncio.CancelledError:
            logger.info("🛑 Pipeline execution cancelled")
        except Exception as e:
            logger.error(f"❌ Pipeline execution error: {e}")
            
            if self.current_pipeline:
                self.current_pipeline.status = PipelineStepStatus.FAILED
                self.current_pipeline.end_time = datetime.now()
                
                await self._broadcast_status_update()
                await self._broadcast_log(f"Pipeline execution failed: {str(e)}", LogLevel.ERROR)
        
        finally:
            self.is_running = False

    async def _execute_step(self, step: PipelineStep):
        """Execute a single pipeline step"""
        logger.info(f"🔄 Executing step: {step.name}")
        
        # Update step status to running
        step.status = PipelineStepStatus.RUNNING
        step.start_time = datetime.now()
        step.progress = 0.0
        
        await self._broadcast_status_update()
        await self._broadcast_log(f"Starting step: {step.name}", LogLevel.INFO)
        
        # Simulate step execution with progress updates
        total_duration = random.uniform(3, 8)  # Random duration between 3-8 seconds
        steps_count = 20
        
        for i in range(steps_count + 1):
            if not self.is_running:
                step.status = PipelineStepStatus.CANCELLED
                break
            
            # Update progress
            step.progress = (i / steps_count) * 100
            self.current_pipeline.overall_progress = self.current_pipeline.calculate_overall_progress()
            
            # Broadcast progress update
            await self._broadcast_status_update()
            
            # Simulate some processing messages
            if i % 5 == 0 and i > 0:
                await self._broadcast_log(
                    f"{step.name}: {step.progress:.1f}% complete", 
                    LogLevel.INFO,
                    step.id
                )
            
            await asyncio.sleep(total_duration / steps_count)
        
        # Complete the step
        if self.is_running:
            step.status = PipelineStepStatus.COMPLETED
            step.end_time = datetime.now()
            step.progress = 100.0
            
            if step.start_time:
                step.duration = (step.end_time - step.start_time).total_seconds()
            
            # Add some output files (demo)
            step.output_files = [f"{step.id}_output.json", f"{step.id}_log.txt"]
            
            await self._broadcast_status_update()
            await self._broadcast_log(f"Completed step: {step.name}", LogLevel.INFO, step.id)
            
            logger.info(f"✅ Step completed: {step.name}")

    async def _broadcast_status_update(self):
        """Broadcast pipeline status update to all clients"""
        if not self.current_pipeline:
            return
        
        await self.websocket_manager.broadcast({
            "type": "pipeline_status",
            "data": self.current_pipeline.dict()
        })

    async def _broadcast_log(self, message: str, level: LogLevel, step_id: Optional[str] = None):
        """Broadcast log message to all clients"""
        log_message = LogMessage(
            level=level,
            message=message,
            step_id=step_id,
            pipeline_id=self.current_pipeline.pipeline_id if self.current_pipeline else None
        )
        
        await self.websocket_manager.broadcast({
            "type": "pipeline_log",
            "data": log_message.dict()
        })

    async def pause_pipeline(self):
        """Pause pipeline execution"""
        if not self.is_running:
            raise ValueError("No pipeline is currently running")
        
        # This is a simplified pause - in a real implementation,
        # you'd need more sophisticated state management
        self.is_running = False
        
        if self.current_pipeline and self.current_pipeline.current_step:
            self.current_pipeline.update_step_status(
                self.current_pipeline.current_step,
                PipelineStepStatus.PAUSED
            )
        
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution paused", LogLevel.WARNING)
        
        logger.info("⏸️ Pipeline execution paused")

    async def resume_pipeline(self):
        """Resume pipeline execution"""
        if self.is_running:
            raise ValueError("Pipeline is already running")
        
        if not self.current_pipeline:
            raise ValueError("No pipeline to resume")
        
        self.is_running = True
        
        # Resume from current step
        if self.current_pipeline.current_step:
            self.current_pipeline.update_step_status(
                self.current_pipeline.current_step,
                PipelineStepStatus.RUNNING
            )
        
        # Restart execution task
        self.execution_task = asyncio.create_task(self._execute_pipeline())
        
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution resumed", LogLevel.INFO)
        
        logger.info("▶️ Pipeline execution resumed")
