"""
Pipeline Routes
Pipeline management endpoints
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime
from typing import Dict, Any

from app.models.schemas import PipelineStartRequest, PipelineResponse
from app.services.pipeline_service import PipelineService
from app.api.dependencies import get_pipeline_service

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/status", response_model=PipelineResponse)
async def get_pipeline_status(
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Get current pipeline status"""
    try:
        status = pipeline_service.get_current_status()
        return PipelineResponse(
            success=True,
            data=status,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"❌ Error getting pipeline status: {e}")
        raise HTTPException(status_code=500, detail={
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

@router.post("/start", response_model=PipelineResponse)
async def start_pipeline(
    request: PipelineStartRequest,
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Start pipeline execution"""
    try:
        await pipeline_service.start_pipeline(request.document_id)
        return PipelineResponse(
            success=True,
            message="Pipeline started successfully",
            data={
                "document_id": request.document_id,
                "pipeline_id": pipeline_service.current_pipeline.pipeline_id if pipeline_service.current_pipeline else None
            },
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"❌ Error starting pipeline: {e}")
        raise HTTPException(status_code=500, detail={
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

@router.post("/stop", response_model=PipelineResponse)
async def stop_pipeline(
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Stop pipeline execution"""
    try:
        await pipeline_service.stop_pipeline()
        return PipelineResponse(
            success=True,
            message="Pipeline stopped successfully",
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"❌ Error stopping pipeline: {e}")
        raise HTTPException(status_code=500, detail={
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

@router.post("/pause", response_model=PipelineResponse)
async def pause_pipeline(
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Pause pipeline execution"""
    try:
        await pipeline_service.pause_pipeline()
        return PipelineResponse(
            success=True,
            message="Pipeline paused successfully",
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"❌ Error pausing pipeline: {e}")
        raise HTTPException(status_code=500, detail={
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

@router.post("/resume", response_model=PipelineResponse)
async def resume_pipeline(
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Resume pipeline execution"""
    try:
        await pipeline_service.resume_pipeline()
        return PipelineResponse(
            success=True,
            message="Pipeline resumed successfully",
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"❌ Error resuming pipeline: {e}")
        raise HTTPException(status_code=500, detail={
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })
