/**
 * Pipeline Navigator Component - UI Only
 * Static pipeline display without logic
 */
import React from "react";
import {
    Upload,
    FileText,
    Play,
    CheckCircle,
    Clock,
    Wifi,
    XCircle,
    AlertCircle,
    Pause,
    RotateCcw,
} from "lucide-react";

// Static pipeline configuration with different statuses
const PIPELINE_STEPS = [
    {
        id: "document_processor",
        name: "Document Processor",
        number: 1,
        status: "completed",
    },
    {
        id: "business_flow_detector",
        name: "Business Flow Detector",
        number: 2,
        status: "running",
    },
    {
        id: "extract_and_process_to_json",
        name: "Extract and Process to JSON",
        number: 3,
        status: "failed",
    },
    {
        id: "gen_html_structure_uidl",
        name: "Generate HTML Structure UIDL",
        number: 4,
        status: "pending",
    },
    {
        id: "gen_test_case",
        name: "Generate Test Case",
        number: 5,
        status: "warning",
    },
    {
        id: "test_case_evaluator",
        name: "Test Case Evaluator",
        number: 6,
        status: "paused",
    },
];

// Function to get status icon and color
const getStatusIcon = (status: string) => {
    switch (status) {
        case "completed":
            return {
                icon: <CheckCircle className="w-4 h-4" />,
                color: "text-green-500",
                text: "Completed",
            };
        case "running":
            return {
                icon: <Clock className="w-4 h-4 animate-spin" />,
                color: "text-yellow-500",
                text: "Running",
            };
        case "failed":
            return {
                icon: <XCircle className="w-4 h-4" />,
                color: "text-red-500",
                text: "Failed",
            };
        case "warning":
            return {
                icon: <AlertCircle className="w-4 h-4" />,
                color: "text-orange-500",
                text: "Warning",
            };
        case "paused":
            return {
                icon: <Pause className="w-4 h-4" />,
                color: "text-blue-500",
                text: "Paused",
            };
        case "pending":
        default:
            return {
                icon: <Clock className="w-4 h-4" />,
                color: "text-gray-500",
                text: "Pending",
            };
    }
};

export const PipelineNavigator: React.FC = () => {
    return (
        <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-700">
                <div className="flex items-center gap-2 mb-3">
                    <Wifi className="w-5 h-5 text-green-500" />
                    <h2 className="text-xl font-bold text-white">
                        Pipeline Navigator
                    </h2>
                </div>
                <p className="text-gray-400 text-sm">
                    Upload a document and execute pipeline steps
                </p>
            </div>

            {/* File Upload Section */}
            <div className="p-4 border-b border-gray-700">
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                        Upload Document
                    </label>
                    <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-gray-500 transition-colors">
                        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-400 mb-1">
                            Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">
                            PDF, DOC, DOCX, TXT files
                        </p>
                    </div>
                </div>

                {/* Current File Display */}
                <div className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-blue-400" />
                        <span className="text-sm text-gray-300">
                            sample-document.pdf
                        </span>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        Uploaded • 2.3 MB
                    </div>
                </div>
            </div>

            {/* Pipeline Steps */}
            <div className="flex-1 overflow-y-auto">
                <div className="p-4">
                    <h3 className="text-sm font-medium text-gray-300 mb-3">
                        Pipeline Steps
                    </h3>
                    <div className="space-y-2">
                        {PIPELINE_STEPS.map((step) => {
                            const statusInfo = getStatusIcon(step.status);
                            return (
                                <div
                                    key={step.id}
                                    className="bg-gray-700 rounded-lg p-3 border border-gray-600"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-xs text-white font-medium">
                                                {step.number}
                                            </div>
                                            <div>
                                                <div className="text-sm font-medium text-white">
                                                    {step.name}
                                                </div>
                                                <div
                                                    className={`text-xs ${statusInfo.color}`}
                                                >
                                                    {statusInfo.text}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <div className={statusInfo.color}>
                                                {statusInfo.icon}
                                            </div>
                                            <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors">
                                                <Play className="w-3 h-3" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>

            {/* Execute All Button */}
            <div className="p-4 border-t border-gray-700">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
                    <Play className="w-4 h-4" />
                    Execute All Steps
                </button>
            </div>
        </div>
    );
};
