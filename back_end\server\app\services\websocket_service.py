"""
WebSocket Connection Manager
Handles multiple WebSocket connections and message broadcasting
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import WebSocket

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manages WebSocket connections and message broadcasting"""
    
    def __init__(self):
        # Store active connections: {client_id: websocket}
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Connection metadata: {client_id: metadata}
        self.connection_metadata: Dict[str, Dict] = {}
        
        # Message queue for offline clients
        self.message_queue: Dict[str, List[Dict]] = {}
        
        # Heartbeat task
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.heartbeat_interval = 30  # seconds
        
        logger.info("🔧 WebSocketManager initialized")

    async def connect(self, websocket: WebSocket) -> str:
        """Add a new WebSocket connection"""
        client_id = str(uuid.uuid4())
        
        # Store connection
        self.active_connections[client_id] = websocket
        
        # Store metadata
        self.connection_metadata[client_id] = {
            "connected_at": datetime.now().isoformat(),
            "last_ping": datetime.now().isoformat(),
            "message_count": 0
        }
        
        # Initialize message queue
        self.message_queue[client_id] = []
        
        # Start heartbeat if this is the first connection
        if len(self.active_connections) == 1:
            await self._start_heartbeat()
        
        logger.info(f"✅ Client {client_id} connected. Total connections: {len(self.active_connections)}")
        
        # Broadcast connection event to other clients
        await self.broadcast({
            "type": "client_connected",
            "data": {
                "client_id": client_id,
                "total_connections": len(self.active_connections)
            }
        }, exclude_client=client_id)
        
        return client_id

    async def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            # Remove connection
            del self.active_connections[client_id]
            
            # Clean up metadata
            if client_id in self.connection_metadata:
                del self.connection_metadata[client_id]
            
            # Clean up message queue
            if client_id in self.message_queue:
                del self.message_queue[client_id]
            
            logger.info(f"❌ Client {client_id} disconnected. Total connections: {len(self.active_connections)}")
            
            # Broadcast disconnection event
            await self.broadcast({
                "type": "client_disconnected", 
                "data": {
                    "client_id": client_id,
                    "total_connections": len(self.active_connections)
                }
            })
            
            # Stop heartbeat if no connections left
            if len(self.active_connections) == 0:
                await self._stop_heartbeat()

    async def disconnect_all(self):
        """Disconnect all clients"""
        logger.info("🛑 Disconnecting all clients...")
        
        # Send shutdown message to all clients
        await self.broadcast({
            "type": "server_shutdown",
            "data": {"message": "Server is shutting down"}
        })
        
        # Close all connections
        for client_id in list(self.active_connections.keys()):
            try:
                websocket = self.active_connections[client_id]
                await websocket.close()
            except Exception as e:
                logger.error(f"❌ Error closing connection {client_id}: {e}")
        
        # Clear all data
        self.active_connections.clear()
        self.connection_metadata.clear()
        self.message_queue.clear()
        
        # Stop heartbeat
        await self._stop_heartbeat()

    async def send_to_client(self, client_id: str, message: Dict[str, Any]) -> bool:
        """Send message to a specific client"""
        if client_id not in self.active_connections:
            logger.warning(f"⚠️ Client {client_id} not found")
            return False
        
        try:
            websocket = self.active_connections[client_id]
            message_json = json.dumps(message)
            await websocket.send_text(message_json)
            
            # Update message count
            if client_id in self.connection_metadata:
                self.connection_metadata[client_id]["message_count"] += 1
            
            logger.debug(f"📤 Sent to {client_id}: {message['type']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending to {client_id}: {e}")
            # Remove broken connection
            await self.disconnect(client_id)
            return False

    async def broadcast(self, message: Dict[str, Any], exclude_client: Optional[str] = None):
        """Broadcast message to all connected clients"""
        if not self.active_connections:
            logger.debug("📢 No clients to broadcast to")
            return
        
        message_json = json.dumps(message)
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            if exclude_client and client_id == exclude_client:
                continue
                
            try:
                await websocket.send_text(message_json)
                
                # Update message count
                if client_id in self.connection_metadata:
                    self.connection_metadata[client_id]["message_count"] += 1
                    
            except Exception as e:
                logger.error(f"❌ Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            await self.disconnect(client_id)
        
        active_count = len(self.active_connections) - (1 if exclude_client else 0)
        logger.debug(f"📢 Broadcasted {message['type']} to {active_count} clients")

    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "connections": [
                {
                    "client_id": client_id,
                    "connected_at": metadata.get("connected_at"),
                    "last_ping": metadata.get("last_ping"),
                    "message_count": metadata.get("message_count", 0)
                }
                for client_id, metadata in self.connection_metadata.items()
            ]
        }

    async def _start_heartbeat(self):
        """Start heartbeat task"""
        if self.heartbeat_task is None or self.heartbeat_task.done():
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            logger.info("💓 Heartbeat started")

    async def _stop_heartbeat(self):
        """Stop heartbeat task"""
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            logger.info("💓 Heartbeat stopped")

    async def _heartbeat_loop(self):
        """Heartbeat loop to keep connections alive"""
        try:
            while True:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.active_connections:
                    break
                
                # Send ping to all clients
                await self.broadcast({
                    "type": "ping",
                    "data": {
                        "timestamp": datetime.now().isoformat(),
                        "server_time": datetime.now().timestamp()
                    }
                })
                
                logger.debug(f"💓 Heartbeat sent to {len(self.active_connections)} clients")
                
        except asyncio.CancelledError:
            logger.info("💓 Heartbeat cancelled")
        except Exception as e:
            logger.error(f"❌ Heartbeat error: {e}")

    def is_client_connected(self, client_id: str) -> bool:
        """Check if a client is connected"""
        return client_id in self.active_connections

    async def update_client_ping(self, client_id: str):
        """Update last ping time for a client"""
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["last_ping"] = datetime.now().isoformat()
