#!/usr/bin/env python3
"""
Test script to check if .env file is being loaded from root directory
"""

import sys
import os
from pathlib import Path

# Add server app directory to Python path
server_dir = Path(__file__).parent / "back_end" / "server"
sys.path.insert(0, str(server_dir))

try:
    # Import settings
    from app.core.config import settings
    
    print("🔍 Testing Configuration Loading from Root Directory...")
    print("=" * 60)
    
    # Check current working directory
    print(f"📂 Current working directory: {os.getcwd()}")
    print(f"📂 Script directory: {Path(__file__).parent}")
    print(f"📂 Server directory: {server_dir}")
    
    # Check if .env files exist
    env_file_server = server_dir / ".env"
    env_file_root = Path(__file__).parent / ".env"
    
    print(f"\n📁 .env in server dir: {env_file_server} (exists: {env_file_server.exists()})")
    print(f"📁 .env in root dir: {env_file_root} (exists: {env_file_root.exists()})")
    
    print(f"\n🔧 Current Settings:")
    print(f"   HOST: {settings.HOST}")
    print(f"   PORT: {settings.PORT}")
    print(f"   DEBUG: {settings.DEBUG}")
    print(f"   RELOAD: {settings.RELOAD}")
    
    print(f"\n🌍 Environment Variables:")
    print(f"   PORT from env: {os.getenv('PORT', 'Not set')}")
    print(f"   HOST from env: {os.getenv('HOST', 'Not set')}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
